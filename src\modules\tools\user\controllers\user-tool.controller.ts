import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  EditToolVersionDto,
  QueryUserToolDto,
  UpdateToolFromAdminDto,
  UserToolDetailDto,
  UserToolListItemDto
} from '../dto';
import { UserToolService } from '../services';

@ApiTags(SWAGGER_API_TAGS.USER_TOOL)
@Controller('user/tools')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserToolController {
  constructor(private readonly userToolService: UserToolService) { }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tool của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tool của người dùng',
    type: () => ApiResponseDto<PaginatedResult<UserToolListItemDto>>,
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async getUserTools(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryUserToolDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserToolListItemDto>>> {
    const result = await this.userToolService.getUserTools(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tool của người dùng' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết tool của người dùng',
    type: () => ApiResponseDto.success(UserToolDetailDto),
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async getUserToolById(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userToolService.getUserToolById(id, userId);
    return ApiResponseDto.success(result);
  }







  @Post(':id/versions/:versionId/edit')
  @ApiOperation({ summary: 'Chỉnh sửa phiên bản tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được chỉnh sửa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async editToolVersion(
    @Param('id') toolId: string,
    @Param('versionId') versionId: string,
    @CurrentUser('id') userId: number,
    @Body() editDto: EditToolVersionDto,
  ) {
    const newVersionId = await this.userToolService.editToolVersion(
      userId,
      toolId,
      versionId,
      editDto,
    );
    return ApiResponseDto.success({ id: newVersionId });
  }

  @Post('clone-all')
  @ApiOperation({
    summary: 'Sao chép tất cả tool công khai đã xuất bản',
    description: 'Sao chép tất cả tool từ admin với điều kiện: isPublic = true, isPublished = true, deletedAt IS NULL. User có thể chọn version mặc định riêng.'
  })
  @ApiResponse({
    status: 200,
    description: 'Số lượng tool đã sao chép thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            count: { type: 'number', example: 15 }
          }
        },
        message: { type: 'string', example: 'Đã sao chép 15 tool thành công' }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_CREATION_FAILED
  )
  async cloneAllTools(
    @CurrentUser('id') userId: number,
  ) {
    const count = await this.userToolService.cloneAllTools(userId);
    return ApiResponseDto.success({ count });
  }

  @Post('update-from-admin')
  @ApiOperation({
    summary: 'Cập nhật user tool từ admin tool tương ứng',
    description: 'Cập nhật tool có hasUpdate = true: tự động cập nhật version cũ và thêm version mới từ admin (isPublished = true). Không cần truyền thêm tham số.'
  })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được cập nhật thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            updatedVersions: { type: 'number', example: 2 },
            addedVersions: { type: 'number', example: 1 },
            newDefaultVersionId: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440001' }
          }
        },
        message: { type: 'string', example: 'Tool đã được cập nhật thành công từ admin' }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
    TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED
  )
  async updateToolFromAdmin(
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateToolFromAdminDto,
  ) {
    const result = await this.userToolService.updateToolFromAdmin(userId, updateDto);
    return ApiResponseDto.success(result);
  }



  @Put(':id/active')
  @ApiOperation({ summary: 'Bật/tắt trạng thái active của tool (đảo ngược trạng thái hiện tại)' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái active đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED
  )
  async toggleToolActive(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    await this.userToolService.toggleToolActive(id, userId);
    return ApiResponseDto.success({ message: 'Trạng thái active đã được cập nhật thành công' });
  }
}
