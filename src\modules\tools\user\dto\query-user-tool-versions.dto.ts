import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

/**
 * DTO cho query danh sách versions của user tool
 */
export class QueryUserToolVersionsDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái version',
    enum: ToolStatusEnum,
    required: false,
    example: ToolStatusEnum.APPROVED
  })
  @IsOptional()
  @IsEnum(ToolStatusEnum, { message: 'Status phải là một trong các giá trị hợp lệ' })
  status?: ToolStatusEnum;

  @ApiProperty({
    description: 'Lọc theo version đã được edit hay chưa',
    type: Boolean,
    required: false,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  edited?: boolean;
}
