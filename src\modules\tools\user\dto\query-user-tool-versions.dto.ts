import { QueryDto } from '@common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export enum UserToolVersionSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho query danh sách versions của user tool
 */
export class QueryUserToolVersionsDto extends QueryDto {

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: UserToolVersionSortBy,
    default: UserToolVersionSortBy.CREATED_AT,
    required: false,
  })
  @IsEnum(UserToolVersionSortBy)
  @IsOptional()
  sortBy?: UserToolVersionSortBy = UserToolVersionSortBy.CREATED_AT;
}
