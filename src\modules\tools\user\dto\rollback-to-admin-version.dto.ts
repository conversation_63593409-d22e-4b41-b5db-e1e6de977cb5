import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc rollback user tool version về admin version tương ứng
 */
export class RollbackToAdminVersionDto {
  @ApiProperty({
    description: 'ID của user tool version cần rollback',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  userToolVersionId: string;
}
