import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc cập nhật tool từ admin với hasUpdate = true
 * Mặc định sẽ cập nhật version hiện tại và thêm version mới từ admin
 */
export class UpdateToolFromAdminDto {
  /**
   * ID của user tool cần cập nhật
   */
  @ApiProperty({
    description: 'ID của user tool cần cập nhật (có hasUpdate = true)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  userToolId: string;
}
