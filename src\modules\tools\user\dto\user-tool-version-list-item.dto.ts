import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho item trong danh sách versions của user tool
 */
export class UserToolVersionListItemDto {
  @ApiProperty({
    description: 'ID của version',
    example: '550e8400-e29b-41d4-a716-446655440000'
  })
  id: string;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0.0'
  })
  versionName: string;

  @ApiProperty({
    description: 'Mô tả tool trong version này',
    example: 'Tool để xử lý dữ liệu JSON'
  })
  toolDescription: string;

  @ApiProperty({
    description: 'Version đã được edit bởi user hay chưa',
    example: false
  })
  edited: boolean;

  @ApiProperty({
    description: 'Có phải version mặc định không',
    example: true
  })
  isDefault: boolean;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1640995200000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1640995200000
  })
  updatedAt: number;
}
