import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { UserToolVersion } from '../../entities';
import {
  UserToolRepository,
  UserToolVersionRepository,
} from '../../repositories';
import {
  UserToolVersionDto,
  QueryUserToolVersionsDto,
  UserToolVersionListItemDto
} from '../dto';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { Transactional } from 'typeorm-transactional';

import { ToolParameterValidatorService } from '../../services/tool-parameter-validator.service';
import { PaginatedResult } from '@common/response';

@Injectable()
export class UserToolVersionService {
  private readonly logger = new Logger(UserToolVersionService.name);

  constructor(
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
    private readonly toolParameterValidator: ToolParameterValidatorService,
  ) {}

  /**
   * <PERSON><PERSON>y thông tin phiên bản
   * @param versionId ID của phiên bản
   * @param userId ID của người dùng
   * @returns Thông tin phiên bản
   */
  async getVersionById(versionId: string, userId: number): Promise<UserToolVersionDto> {
    try {
      // Lấy thông tin phiên bản
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Chuyển đổi sang DTO
      return this.mapVersionToDto(version);
    } catch (error) {
      this.logger.error(`Failed to get version by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND, error.message);
    }
  }

  /**
   * Lấy danh sách versions theo tool ID
   * @param toolId ID của tool
   * @param userId ID của người dùng
   * @param query Query parameters
   * @returns Danh sách versions với phân trang
   */
  async getVersionsByToolId(
    toolId: string,
    userId: number,
    query: QueryUserToolVersionsDto,
  ): Promise<PaginatedResult<UserToolVersionListItemDto>> {
    try {
      // Kiểm tra tool có tồn tại và thuộc về user không
      const tool = await this.userToolRepository.findToolById(toolId, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy danh sách versions với phân trang
      const result = await this.userToolVersionRepository.findVersionsByToolIdWithPagination(
        toolId,
        userId,
        query,
      );

      // Map entities sang DTOs
      const mappedItems = result.items.map(version => this.mapVersionToListItemDto(version, tool.versionDefaultId || undefined));

      return {
        items: mappedItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to get versions by tool ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND, error.message);
    }
  }

  /**
   * Đặt phiên bản làm mặc định
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param userId ID của người dùng
   * @returns true nếu thành công
   */
  @Transactional()
  async setDefaultVersion(
    toolId: string,
    versionId: string,
    userId: number,
  ): Promise<boolean> {
    try {
      this.logger.log(`Setting default version: toolId=${toolId}, versionId=${versionId}, userId=${userId}`);

      // Kiểm tra tool có tồn tại không và thuộc về người dùng không
      const tool = await this.userToolRepository.findToolById(toolId, userId);
      if (!tool) {
        this.logger.error(`Tool not found: toolId=${toolId}, userId=${userId}`);
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }
      this.logger.log(`Found tool: ${tool.id}, name: ${tool.name}`);

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool và người dùng không
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version) {
        this.logger.error(`Version not found: versionId=${versionId}, userId=${userId}`);
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      if (version.originalToolId !== toolId) {
        this.logger.error(`Version does not belong to tool: version.originalToolId=${version.originalToolId}, toolId=${toolId}`);
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }
      this.logger.log(`Found version: ${version.id}, versionName: ${version.versionName}`);

      // Cập nhật version mặc định và thời gian cập nhật cho tool bằng update query trực tiếp
      const updateResult = await this.userToolRepository.update(
        { id: toolId, userId: userId },
        {
          versionDefaultId: versionId,
          updatedAt: Date.now()
        }
      );

      if (updateResult.affected === 0) {
        this.logger.error(`Failed to update tool: no rows affected`);
        throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED);
      }

      this.logger.log(`Updated tool ${toolId}: versionDefaultId=${versionId}, affected=${updateResult.affected}`);

      this.logger.log(`Successfully set version ${versionId} as default for user tool ${toolId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to set default version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Validate tool parameters schema (dành cho admin khi tạo/cập nhật tool)
   * @param parameters Schema parameters cần validate
   * @returns true nếu hợp lệ
   */
  validateToolParametersSchema(parameters: Record<string, unknown>): boolean {
    return this.toolParameterValidator.validateToolParameters(parameters);
  }



  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(version: UserToolVersion): UserToolVersionDto {
    const versionDto = new UserToolVersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName || '';
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.createdAt = version.createdAt;
    versionDto.edited = version.edited;
    return versionDto;
  }

  /**
   * Chuyển đổi phiên bản sang List Item DTO
   * @param version Phiên bản
   * @param defaultVersionId ID của version mặc định
   * @returns List Item DTO của phiên bản
   */
  private mapVersionToListItemDto(version: UserToolVersion, defaultVersionId?: string): UserToolVersionListItemDto {
    const dto = new UserToolVersionListItemDto();
    dto.id = version.id;
    dto.versionName = version.versionName || '';
    dto.toolDescription = version.toolDescription || '';
    dto.status = version.status;
    dto.edited = version.edited;
    dto.isDefault = version.id === defaultVersionId;
    dto.createdAt = version.createdAt;
    dto.updatedAt = version.updatedAt;
    dto.originalVersionId = version.originalVersionId || undefined;
    return dto;
  }
}
