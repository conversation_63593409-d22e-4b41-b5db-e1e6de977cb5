import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { UserTool, UserToolVersion } from '../../entities';
import {
  UserToolRepository,
  UserToolVersionRepository,
  AdminToolRepository,
  AdminToolVersionRepository,
} from '../../repositories';
import {
  CloneAdminToolDto,
  CloneAllPublicToolsDto,
  EditToolVersionDto,
  QueryUserToolDto,
  RollbackToAdminVersionDto,
  UpdateFromAdminDto,
  UpdateToolFromAdminDto,
  UserToolDetailDto,
  UserToolListItemDto,
} from '../dto';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ToolStatusEnum } from '../../constants/tool-status.enum';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class UserToolService {
  private readonly logger = new Logger(UserToolService.name);

  constructor(
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
  ) {}

  /**
   * Lấy danh sách tool của người dùng với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getUserTools(
    userId: number,
    queryDto: QueryUserToolDto,
  ): Promise<PaginatedResult<UserToolListItemDto>> {
    try {
      const { page, limit, search, hasUpdate, sortBy, sortDirection } = queryDto;

      // Lấy danh sách tool của người dùng (đã loại bỏ group info)
      const result = await this.userToolRepository.findUserTools(
        page,
        limit,
        userId,
        search,
        ToolStatusEnum.APPROVED, // Chỉ lấy tool có trạng thái APPROVED
        hasUpdate,
        sortBy,
        sortDirection,
      );

      // Group các tool theo ID và tập hợp groups
      const toolsMap = new Map<string, any>();

      result.items.forEach((item: any) => {
        if (!toolsMap.has(item.tool_id)) {
          toolsMap.set(item.tool_id, {
            id: item.tool_id,
            name: item.tool_name,
            description: item.tool_description,
            createdAt: item.tool_createdat,
            updatedAt: item.tool_updatedat,
            originalId: item.tool_originalid,
            hasUpdate: item.tool_hasupdate,
            active: item.tool_active,
          });
        }

        // Thêm group nếu có
        if (item.grouptool_id && item.grouptool_name) {
          const tool = toolsMap.get(item.tool_id);
          const existingGroup = tool.groups.find((g: any) => g.id === item.grouptool_id);
          if (!existingGroup) {
            tool.groups.push({
              id: item.grouptool_id,
              name: item.grouptool_name
            });
          }
        }
      });

      // Chuyển đổi sang DTO
      const items = Array.from(toolsMap.values()).map((tool) => {
        const dto = new UserToolListItemDto();
        dto.id = tool.id;
        dto.name = tool.name;
        dto.description = tool.description;
        dto.createdAt = tool.createdAt;
        dto.updatedAt = tool.updatedAt;
        dto.hasUpdate = tool.hasUpdate;
        dto.active = tool.active;

        return dto;
      });

      return {
        items,
        meta: {
          totalItems: result.total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết tool của người dùng
   * @param toolId ID của tool
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết tool
   */
  async getUserToolById(toolId: string, userId: number): Promise<UserToolDetailDto> {
    try {
      // Lấy thông tin tool (đã loại bỏ groups)
      const toolWithGroups = await this.userToolRepository.findToolByIdDetailed(toolId, userId);
      if (!toolWithGroups || toolWithGroups.length === 0) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy thông tin tool đầu tiên
      const toolInfo = toolWithGroups[0];

      // Lấy tất cả phiên bản của tool
      const versions = await this.userToolVersionRepository.findVersionsByToolIdAndUserId(toolId, userId);

      // Lấy phiên bản mặc định (phiên bản mới nhất)
      const defaultVersion = versions.length > 0 ? versions[0] : null;

      // Chuyển đổi sang DTO
      const dto = new UserToolDetailDto();
      dto.id = toolInfo.tool_id;
      dto.name = toolInfo.tool_name;
      dto.description = toolInfo.tool_description;
      dto.createdAt = toolInfo.tool_createdat;
      dto.updatedAt = toolInfo.tool_updatedat;
      dto.hasUpdate = toolInfo.tool_hasupdate;

      // Map default version
      if (defaultVersion) {
        dto.defaultVersion = {
          id: defaultVersion.id,
          versionName: defaultVersion.versionName || null, // Fallback nếu null
          toolDescription: defaultVersion.toolDescription,
          parameters: defaultVersion.parameters,
          createdAt: defaultVersion.createdAt,
          edited: defaultVersion.edited,
        };
      } else {
        dto.defaultVersion = null;
      }

      // Map versions (simple)
      dto.versions = versions.map(version => ({
        id: version.id,
        versionName: version.versionName || null, // Fallback nếu null
      }));

      return dto;
    } catch (error) {
      this.logger.error(`Failed to get user tool by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Sao chép tool từ admin
   * @param userId ID của người dùng
   * @param cloneDto Dữ liệu sao chép
   * @returns ID của tool đã sao chép
   */
  @Transactional()
  async cloneAdminTool(
    userId: number,
    cloneDto: CloneAdminToolDto,
  ): Promise<string> {
    try {
      const { adminToolId } = cloneDto;

      // Kiểm tra tool admin có tồn tại và thỏa mãn điều kiện clone
      const adminTool = await this.adminToolRepository.findToolById(adminToolId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra tool admin phải thỏa mãn điều kiện: isPublic = true, isPublished = true, deletedAt IS NULL
      if (!adminTool.isPublic) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
          'Tool này không phải là công khai và không thể sao chép.'
        );
      }

      if (!adminTool.isPublished) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này chưa được xuất bản và không thể sao chép.'
        );
      }

      if (adminTool.deletedAt) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không thể sao chép.'
        );
      }

      // Kiểm tra người dùng đã sao chép tool này chưa
      const existingTool = await this.userToolRepository.findToolByOriginalId(
        adminToolId,
        userId,
      );
      if (existingTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
      }

      // Tạo tool mới cho người dùng
      const newTool = new UserTool();
      newTool.name = cloneDto.customName || adminTool.name;
      newTool.description = cloneDto.customDescription || adminTool.description;
      newTool.userId = userId;
      newTool.originalId = adminToolId;
      newTool.status = ToolStatusEnum.APPROVED;
      newTool.hasUpdate = false;

      // Lưu tool
      const savedTool = await this.userToolRepository.save(newTool);

      // Lấy tất cả các phiên bản của tool admin thỏa mãn điều kiện
      const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminToolId);
      const validVersions = adminVersions.filter(version =>
        version.isPublicshed // Phải được xuất bản
      );

      if (validVersions.length === 0) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
          'Tool này không có phiên bản nào khả dụng để sao chép.'
        );
      }

      // Clone tất cả các phiên bản hợp lệ và xác định version mặc định
      let defaultVersionId: string | null = null;
      for (const adminVersion of validVersions) {
        const newVersion = new UserToolVersion();
        newVersion.userId = userId;
        newVersion.originalToolId = savedTool.id;
        newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
        newVersion.toolDescription = adminVersion.toolDescription;
        newVersion.parameters = adminVersion.parameters;
        newVersion.status = ToolStatusEnum.APPROVED;
        newVersion.edited = false;
        newVersion.versionName = adminVersion.versionName;

        // Lưu phiên bản
        const savedVersion = await this.userToolVersionRepository.save(newVersion);

        // Đặt version đầu tiên làm mặc định hoặc version mặc định của admin tool
        if (!defaultVersionId || adminVersion.id === adminTool.versionDefault) {
          defaultVersionId = savedVersion.id;
        }
      }

      // Cập nhật version mặc định cho user tool
      if (defaultVersionId) {
        savedTool.versionDefaultId = defaultVersionId;
        await this.userToolRepository.save(savedTool);
      }

      return savedTool.id;
    } catch (error) {
      this.logger.error(`Failed to clone admin tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật tool từ phiên bản mới của admin
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của tool đã cập nhật
   */
  @Transactional()
  async updateFromAdmin(
    userId: number,
    updateDto: UpdateFromAdminDto,
  ): Promise<string> {
    try {
      const { userToolId } = updateDto;

      // Kiểm tra tool người dùng có tồn tại không
      const userTool = await this.userToolRepository.findToolById(userToolId, userId);
      if (!userTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra tool có phải là tool được sao chép từ admin không
      if (!userTool.originalId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED);
      }

      // Kiểm tra tool admin có tồn tại không
      const adminTool = await this.adminToolRepository.findToolById(userTool.originalId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy phiên bản mới nhất của tool admin
      const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
      if (!adminVersion) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Tạo phiên bản mới
      const newVersion = new UserToolVersion();
      newVersion.userId = userId;
      newVersion.originalToolId = userToolId;
      newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
      // newVersion.toolName = adminVersion.toolName; // Field không tồn tại trong entity
      newVersion.toolDescription = adminVersion.toolDescription;
      newVersion.parameters = adminVersion.parameters;
      // newVersion.changeDescription = `Cập nhật từ phiên bản admin ${adminVersion.versionName}`; // Field không tồn tại
      newVersion.status = ToolStatusEnum.APPROVED;
      newVersion.edited = false;

      // Lưu phiên bản
      await this.userToolVersionRepository.save(newVersion);

      // Cập nhật tool
      userTool.name = adminTool.name;
      userTool.description = adminTool.description;
      userTool.hasUpdate = false;
      userTool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(userTool);

      return userTool.id;
    } catch (error) {
      this.logger.error(`Failed to update from admin: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Sao chép tất cả tool công khai từ admin
   * @param userId ID của người dùng
   * @param cloneDto Dữ liệu sao chép
   * @returns Số lượng tool đã sao chép
   */
  @Transactional()
  async cloneAllPublicTools(
    userId: number,
    cloneDto: CloneAllPublicToolsDto,
  ): Promise<number> {
    try {
      const { namePrefix } = cloneDto;

      // Lấy danh sách tất cả tool công khai của admin từ repository
      const publicTools = await this.adminToolRepository.findAllPublicApprovedTools();

      if (publicTools.length === 0) {
        return 0;
      }

      // Lấy danh sách tool đã được sao chép bởi người dùng từ repository
      const existingTools = await this.userToolRepository.findClonedToolsByUser(userId);

      // Tạo map các tool đã được sao chép
      const existingToolMap = new Map<string, UserTool>();
      existingTools.forEach(tool => {
        if (tool.originalId) {
          existingToolMap.set(tool.originalId, tool);
        }
      });

      // Lọc các tool chưa được sao chép
      const toolsToClone = publicTools.filter(tool => !existingToolMap.has(tool.id));

      if (toolsToClone.length === 0) {
        return 0;
      }

      // Sao chép từng tool
      let clonedCount = 0;
      for (const adminTool of toolsToClone) {
        try {
          // Lấy tất cả các phiên bản có trạng thái APPROVED của tool admin
          const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminTool.id);
          const approvedVersions = adminVersions.filter(version => version.isPublicshed); // Sử dụng isPublicshed

          if (approvedVersions.length === 0) {
            continue;
          }

          // Tạo tool mới cho người dùng
          const newTool = new UserTool();
          newTool.name = namePrefix ? `${namePrefix}${adminTool.name}` : adminTool.name;
          newTool.description = adminTool.description;
          newTool.userId = userId;
          newTool.originalId = adminTool.id;
          newTool.status = ToolStatusEnum.APPROVED;
          newTool.hasUpdate = false;

          // Lưu tool
          const savedTool = await this.userToolRepository.save(newTool);

          // Clone tất cả các phiên bản APPROVED
          for (const adminVersion of approvedVersions) {
            const newVersion = new UserToolVersion();
            newVersion.userId = userId;
            newVersion.originalToolId = savedTool.id;
            newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
            newVersion.toolDescription = adminVersion.toolDescription;
            newVersion.parameters = adminVersion.parameters;
            newVersion.status = ToolStatusEnum.APPROVED;
            newVersion.edited = false;

            // Lưu phiên bản
            await this.userToolVersionRepository.save(newVersion);
          }

          clonedCount++;
        } catch (error) {
          this.logger.error(`Failed to clone tool ${adminTool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      return clonedCount;
    } catch (error) {
      this.logger.error(`Failed to clone all public tools: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Chỉnh sửa phiên bản tool của người dùng
   * @param userId ID của người dùng
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param editDto Dữ liệu chỉnh sửa
   * @returns ID của phiên bản mới
   */
  @Transactional()
  async editToolVersion(
    userId: number,
    toolId: string,
    versionId: string,
    editDto: EditToolVersionDto,
  ): Promise<string> {
    try {
      // Kiểm tra tool có tồn tại không và thuộc về người dùng không
      const tool = await this.userToolRepository.findToolById(toolId, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool và người dùng không
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version || version.originalToolId !== toolId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Cập nhật phiên bản hiện tại (không tạo version mới)
      // Chỉ cập nhật các trường được phép, không được sửa toolName
      const updateData: Partial<UserToolVersion> = {};

      if (editDto.toolDescription !== undefined) {
        updateData.toolDescription = editDto.toolDescription;
      }

      if (editDto.parameters !== undefined) {
        updateData.parameters = editDto.parameters;
      }

      if (editDto.changeDescription !== undefined) {
        // updateData.changeDescription = editDto.changeDescription; // Field không tồn tại
      }

      // Đánh dấu là đã được chỉnh sửa
      updateData.edited = true;
      updateData.updatedAt = Date.now();

      // Sử dụng update thay vì save
      const updateResult = await this.userToolVersionRepository.update(
        { id: versionId, userId: userId },
        updateData as any // Cast to any để tránh lỗi type
      );

      if (updateResult.affected === 0) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_UPDATE_FAILED);
      }

      // Cập nhật thời gian cập nhật cho tool
      tool.updatedAt = Date.now();
      await this.userToolRepository.update(
        { id: toolId, userId: userId },
        { updatedAt: tool.updatedAt }
      );

      return versionId; // Trả về ID của version đã được cập nhật
    } catch (error) {
      this.logger.error(`Failed to edit tool version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_EDIT_FAILED, error.message);
    }
  }

  /**
   * Khôi phục về phiên bản gốc từ admin
   * @param userId ID của người dùng
   * @param rollbackDto Dữ liệu khôi phục
   * @returns ID của tool đã khôi phục
   */
  @Transactional()
  async rollbackToAdminVersion(
    userId: number,
    rollbackDto: RollbackToAdminVersionDto,
  ): Promise<string> {
    try {
      const { userToolId } = rollbackDto;

      // Kiểm tra tool người dùng có tồn tại không
      const userTool = await this.userToolRepository.findToolById(userToolId, userId);
      if (!userTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra tool có phải là tool được sao chép từ admin không
      if (!userTool.originalId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED);
      }

      // Kiểm tra tool admin có tồn tại không
      const adminTool = await this.adminToolRepository.findToolById(userTool.originalId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy phiên bản mới nhất của tool admin
      const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
      if (!adminVersion) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Tạo phiên bản mới
      const newVersion = new UserToolVersion();
      newVersion.userId = userId;
      newVersion.originalToolId = userToolId;
      newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
      newVersion.toolDescription = adminVersion.toolDescription;
      newVersion.parameters = adminVersion.parameters;
      newVersion.status = ToolStatusEnum.APPROVED;
      newVersion.edited = false;

      // Lưu phiên bản
      await this.userToolVersionRepository.save(newVersion);

      // Cập nhật tool
      userTool.name = adminTool.name;
      userTool.description = adminTool.description;
      userTool.hasUpdate = false;
      userTool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(userTool);

      return userTool.id;
    } catch (error) {
      this.logger.error(`Failed to rollback to admin version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_ROLLBACK_FAILED, error.message);
    }
  }



  /**
   * Bật/tắt trạng thái active của tool
   * @param id ID của tool cần cập nhật
   * @param userId ID của người dùng
   * @returns true nếu cập nhật thành công
   */
  @Transactional()
  async toggleToolActive(id: string, userId: number): Promise<boolean> {
    try {
      // Lấy thông tin tool
      const tool = await this.userToolRepository.findToolById(id, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Đảo ngược trạng thái active
      tool.active = !tool.active;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(tool);

      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái active: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Sao chép tất cả tool có trạng thái APPROVED và loại PUBLIC
   * @param userId ID của người dùng
   * @returns Số lượng tool đã sao chép
   */
  @Transactional()
  async cloneAllTools(
    userId: number,
  ): Promise<number> {
    try {
      this.logger.log(`Starting clone all tools for user ${userId}`);

      // Lấy danh sách tất cả tool thỏa mãn điều kiện: isPublic = true, isPublished = true, deletedAt IS NULL
      const approvedTools = await this.adminToolRepository.findAllPublicPublishedTools();
      this.logger.log(`Found ${approvedTools.length} PUBLIC PUBLISHED admin tools`);

      if (approvedTools.length === 0) {
        this.logger.log(`No PUBLIC APPROVED admin tools found for user ${userId}`);
        return 0;
      }

      // Lấy danh sách tool đã được sao chép bởi người dùng từ repository
      const existingTools = await this.userToolRepository.findClonedToolsByUser(userId);
      this.logger.log(`User ${userId} has already cloned ${existingTools.length} tools`);

      // Tạo map các tool đã được sao chép
      const existingToolMap = new Map<string, UserTool>();
      existingTools.forEach(tool => {
        if (tool.originalId) {
          existingToolMap.set(tool.originalId, tool);
        }
      });

      // Lọc các tool chưa được sao chép
      const toolsToClone = approvedTools.filter(tool => !existingToolMap.has(tool.id));
      this.logger.log(`Found ${toolsToClone.length} tools to clone for user ${userId}`);

      if (toolsToClone.length === 0) {
        this.logger.log(`All tools already cloned for user ${userId}`);
        return 0;
      }

      // Sao chép từng tool
      let clonedCount = 0;
      for (const adminTool of toolsToClone) {
        try {
          this.logger.log(`Cloning tool ${adminTool.id} (${adminTool.name}) for user ${userId}`);

          // Lấy tất cả các phiên bản có trạng thái PUBLISHED của tool admin
          const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminTool.id);
          const publishedVersions = adminVersions.filter(version => version.isPublicshed); // Sử dụng isPublicshed (typo trong entity)

          this.logger.log(`Tool ${adminTool.id} has ${adminVersions.length} total versions, ${publishedVersions.length} published versions`);

          if (publishedVersions.length === 0) {
            this.logger.warn(`No published versions found for tool ${adminTool.id}, skipping`);
            continue;
          }

          // Tạo tool mới cho người dùng với tiền tố userId_
          const newTool = new UserTool();
          newTool.name = adminTool.name;
          newTool.description = adminTool.description;
          newTool.userId = userId;
          newTool.originalId = adminTool.id;
          newTool.status = ToolStatusEnum.APPROVED;
          newTool.hasUpdate = false;

          // Lưu tool
          const savedTool = await this.userToolRepository.save(newTool);

          // Clone tất cả các phiên bản PUBLISHED và xác định version mặc định
          let defaultVersionId: string | null = null;
          for (const adminVersion of publishedVersions) {
            const newVersion = new UserToolVersion();
            newVersion.userId = userId;
            newVersion.originalToolId = savedTool.id;
            newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
            newVersion.toolDescription = adminVersion.toolDescription;
            newVersion.parameters = adminVersion.parameters;
            newVersion.status = ToolStatusEnum.APPROVED;
            newVersion.edited = false;
            newVersion.versionName = adminVersion.versionName;

            // Lưu phiên bản
            const savedVersion = await this.userToolVersionRepository.save(newVersion);

            // Đặt version đầu tiên làm mặc định hoặc version mặc định của admin tool
            if (!defaultVersionId || adminVersion.id === adminTool.versionDefault) {
              defaultVersionId = savedVersion.id;
            }
          }

          // Cập nhật version mặc định cho user tool
          if (defaultVersionId) {
            savedTool.versionDefaultId = defaultVersionId;
            await this.userToolRepository.save(savedTool);
          }

          clonedCount++;
          this.logger.log(`Successfully cloned tool ${adminTool.id} for user ${userId}. Total cloned: ${clonedCount}`);
        } catch (error) {
          this.logger.error(`Failed to clone tool ${adminTool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      this.logger.log(`Clone operation completed for user ${userId}. Total tools cloned: ${clonedCount}`);
      return clonedCount;
    } catch (error) {
      this.logger.error(`Failed to clone all tools: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật tất cả các tool có hasUpdate=true với phiên bản mới nhất từ admin
   * @param userId ID của người dùng
   * @returns Số lượng tool đã cập nhật
   */
  @Transactional()
  async updateAllToolsWithNewVersion(userId: number): Promise<number> {
    try {
      // Lấy danh sách tất cả tool của người dùng có hasUpdate=true từ repository
      const tools = await this.userToolRepository.findToolsWithUpdate(userId);

      if (tools.length === 0) {
        return 0;
      }

      let updatedCount = 0;

      // Cập nhật từng tool
      for (const tool of tools) {
        try {
          if (!tool.originalId) continue;

          // Kiểm tra tool admin có tồn tại không
          const adminTool = await this.adminToolRepository.findToolById(tool.originalId);
          if (!adminTool) continue;

          // Lấy phiên bản mới nhất của tool admin
          const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
          if (!adminVersion) continue;

          // Tạo phiên bản mới
          const newVersion = new UserToolVersion();
          newVersion.userId = userId;
          newVersion.originalToolId = tool.id;
          newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
          newVersion.toolDescription = adminVersion.toolDescription;
          newVersion.parameters = adminVersion.parameters;
          newVersion.status = ToolStatusEnum.APPROVED;
          newVersion.edited = false;

          // Lưu phiên bản
          await this.userToolVersionRepository.save(newVersion);

          // Cập nhật tool
          tool.name = adminTool.name;
          tool.description = adminTool.description;
          tool.hasUpdate = false;
          tool.updatedAt = Date.now();

          // Lưu tool
          await this.userToolRepository.save(tool);

          updatedCount++;
        } catch (error) {
          this.logger.error(`Lỗi khi cập nhật tool ${tool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      return updatedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tất cả tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật user tool từ admin tool tương ứng
   * - Cập nhật version cũ theo admin
   * - Thêm version mới từ admin nếu có (isPublished = true)
   * @param userId ID của người dùng
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Thông tin kết quả cập nhật
   */
  @Transactional()
  async updateToolFromAdmin(
    userId: number,
    updateDto: UpdateToolFromAdminDto,
  ): Promise<{
    updatedVersions: number;
    addedVersions: number;
    newDefaultVersionId?: string;
  }> {
    try {
      // Kiểm tra user tool có tồn tại và có hasUpdate = true
      const userTool = await this.userToolRepository.findOne({
        where: { id: updateDto.userToolId, userId, hasUpdate: true },
      });

      if (!userTool) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'User tool không tồn tại hoặc không có cập nhật từ admin.'
        );
      }

      if (!userTool.originalId) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
          'User tool không có tham chiếu đến admin tool.'
        );
      }

      // Lấy admin tool gốc
      const adminTool = await this.adminToolRepository.findToolById(userTool.originalId);
      if (!adminTool) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Admin tool gốc không tồn tại.'
        );
      }

      // Kiểm tra admin tool vẫn thỏa mãn điều kiện
      if (!adminTool.isPublic || !adminTool.isPublished || adminTool.deletedAt) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
          'Admin tool không còn khả dụng để cập nhật.'
        );
      }

      let updatedVersions = 0;
      let addedVersions = 0;
      let newDefaultVersionId: string | undefined;

      // 1. Cập nhật version hiện tại nếu được yêu cầu
      if (updateDto.updateExistingVersions) {
        updatedVersions = await this.updateExistingVersionsFromAdmin(
          userTool.id,
        );
      }

      // 2. Thêm version mới nếu được yêu cầu
      if (updateDto.addNewVersions) {
        const result = await this.addNewVersionsFromAdmin(
          userTool.id,
          adminTool.id,
          userId
        );
        addedVersions = result.addedCount;
        newDefaultVersionId = result.newDefaultVersionId;
      }

      // 3. Cập nhật thông tin tool và reset hasUpdate
      userTool.name = adminTool.name;
      userTool.description = adminTool.description;
      userTool.hasUpdate = false;
      userTool.updatedAt = Date.now();

      // Cập nhật version mặc định nếu có version mới
      if (newDefaultVersionId) {
        userTool.versionDefaultId = newDefaultVersionId;
      }

      await this.userToolRepository.save(userTool);

      this.logger.log(
        `Updated user tool ${userTool.id}: ${updatedVersions} versions updated, ${addedVersions} versions added`
      );

      return {
        updatedVersions,
        addedVersions,
        newDefaultVersionId,
      };
    } catch (error) {
      this.logger.error(`Failed to update tool from admin: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật các version hiện tại từ admin
   * @param userToolId ID của user tool
   * @param adminToolId ID của admin tool
   * @param userId ID của user
   * @returns Số lượng version đã cập nhật
   */
  private async updateExistingVersionsFromAdmin(
    userToolId: string,
  ): Promise<number> {
    try {
      // Lấy tất cả user tool versions
      const userVersions = await this.userToolVersionRepository.find({
        where: { originalToolId: userToolId },
      });

      let updatedCount = 0;

      for (const userVersion of userVersions) {
        if (!userVersion.originalVersionId) continue;

        // Lấy admin version tương ứng
        const adminVersion = await this.adminToolVersionRepository.findVersionById(
          userVersion.originalVersionId
        );

        if (!adminVersion || !adminVersion.isPublicshed) continue;

        // Cập nhật user version theo admin version (chỉ nếu chưa được edit)
        if (!userVersion.edited) {
          userVersion.versionName = adminVersion.versionName;
          userVersion.toolDescription = adminVersion.toolDescription;
          userVersion.parameters = adminVersion.parameters;
          userVersion.updatedAt = Date.now();

          await this.userToolVersionRepository.save(userVersion);
          updatedCount++;
        }
      }

      return updatedCount;
    } catch (error) {
      this.logger.error(`Error updating existing versions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Thêm các version mới từ admin
   * @param userToolId ID của user tool
   * @param adminToolId ID của admin tool
   * @param userId ID của user
   * @returns Thông tin về version đã thêm
   */
  private async addNewVersionsFromAdmin(
    userToolId: string,
    adminToolId: string,
    userId: number,
  ): Promise<{ addedCount: number; newDefaultVersionId?: string }> {
    try {
      // Lấy tất cả admin versions đã published
      const adminVersions = await this.adminToolVersionRepository.find({
        where: { toolId: adminToolId, isPublicshed: true },
        order: { createdAt: 'ASC' },
      });

      // Lấy tất cả user versions hiện tại
      const existingUserVersions = await this.userToolVersionRepository.find({
        where: { originalToolId: userToolId },
      });

      // Tìm admin versions chưa có trong user versions
      const existingOriginalVersionIds = existingUserVersions
        .map(v => v.originalVersionId)
        .filter(Boolean);

      const newAdminVersions = adminVersions.filter(
        adminVersion => !existingOriginalVersionIds.includes(adminVersion.id)
      );

      let addedCount = 0;
      let newDefaultVersionId: string | undefined;

      for (const adminVersion of newAdminVersions) {
        // Tạo user tool version mới
        const newUserVersion = this.userToolVersionRepository.create({
          userId: userId,
          originalToolId: userToolId,
          originalVersionId: adminVersion.id,
          versionName: adminVersion.versionName,
          toolDescription: adminVersion.toolDescription,
          parameters: adminVersion.parameters,
          status: ToolStatusEnum.APPROVED,
          edited: false,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        const savedVersion = await this.userToolVersionRepository.save(newUserVersion);
        addedCount++;

        // Nếu đây là version mặc định của admin tool, đánh dấu làm mặc định cho user
        const adminTool = await this.adminToolRepository.findToolById(adminToolId);
        if (adminTool && adminVersion.id === adminTool.versionDefault) {
          newDefaultVersionId = savedVersion.id;
        }
      }

      return { addedCount, newDefaultVersionId };
    } catch (error) {
      this.logger.error(`Error adding new versions: ${error.message}`, error.stack);
      throw error;
    }
  }
}
